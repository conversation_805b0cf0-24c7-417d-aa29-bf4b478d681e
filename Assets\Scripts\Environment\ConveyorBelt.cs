using UnityEngine;
using Fusion;
using SimpleFPS;
using System.Collections.Generic;

namespace SimpleFPS {
    /// <summary>
    /// Бегущая лента, которая двигает любые объекты, попавшие на неё
    /// </summary>
    public class ConveyorBelt : NetworkBehaviour {
        [Header("Conveyor Settings")]
        [SerializeField] private Vector3 beltDirection = Vector3.forward;
        [SerializeField] private float beltSpeed = 5f;
        [SerializeField] private bool isActive = true;
        
        [Header("Detection")]
        [SerializeField] private LayerMask affectedLayers = -1; // Все слои по умолчанию
        [SerializeField] private float detectionHeight = 0.5f; // Высота над лентой для обнаружения объектов
        
        [Header("Visual")]
        [SerializeField] private Renderer beltRenderer;
        [SerializeField] private string texturePropertyName = "_MainTex";
        
        // Локальные переменные
        private BoxCollider beltCollider;
        private List<Rigidbody> objectsOnBelt = new List<Rigidbody>();
        private List<PlayerController> playersOnBelt = new List<PlayerController>();
        private Vector2 textureOffset;
        
        // Сетевые переменные
        [Networked] public bool IsActive { get; set; }
        [Networked] public float Speed { get; set; }
        
        public override void Spawned() {
            // Инициализация сетевых переменных
            if (Object.HasStateAuthority) {
                IsActive = isActive;
                Speed = beltSpeed;
            }
            
            // Получаем компоненты
            beltCollider = GetComponent<BoxCollider>();
            if (beltCollider == null) {
                Debug.LogError("ConveyorBelt требует BoxCollider компонент!");
                return;
            }
            
            // Убеждаемся что коллайдер настроен как триггер
            beltCollider.isTrigger = true;
            
            // Нормализуем направление ленты
            beltDirection = beltDirection.normalized;
        }
        
        public override void FixedUpdateNetwork() {
            if (!IsActive) return;
            
            // Обновляем движение объектов на ленте
            MoveObjectsOnBelt();
            
            // Обновляем движение игроков на ленте
            MovePlayersOnBelt();
        }
        
        public override void Render() {
            // Обновляем визуальную анимацию текстуры
            if (beltRenderer != null && IsActive) {
                textureOffset.x += Speed * Time.deltaTime * 0.1f; // Масштабируем для визуального эффекта
                beltRenderer.material.SetTextureOffset(texturePropertyName, textureOffset);
            }
        }
        
        private void MoveObjectsOnBelt() {
            // Очищаем список от null объектов
            objectsOnBelt.RemoveAll(rb => rb == null);
            
            Vector3 beltForce = transform.TransformDirection(beltDirection) * Speed;
            
            foreach (Rigidbody rb in objectsOnBelt) {
                if (rb != null && !rb.isKinematic) {
                    // Применяем силу в направлении ленты
                    rb.AddForce(beltForce, ForceMode.Acceleration);
                }
            }
        }
        
        private void MovePlayersOnBelt() {
            // Очищаем список от null игроков
            playersOnBelt.RemoveAll(player => player == null);
            
            Vector3 beltVelocity = transform.TransformDirection(beltDirection) * Speed;
            
            foreach (PlayerController player in playersOnBelt) {
                if (player != null && player.health.IsAlive) {
                    // Применяем дополнительную скорость к игроку через KCC
                    if (player.KCC != null) {
                        // Добавляем скорость ленты к текущему движению игрока
                        player.RPC_ApplyKnockback(beltVelocity * Runner.DeltaTime);
                    }
                }
            }
        }
        
        private void OnTriggerEnter(Collider other) {
            if (!Object.HasStateAuthority) return;
            
            // Проверяем слой объекта
            if (((1 << other.gameObject.layer) & affectedLayers) == 0) return;
            
            // Проверяем игроков
            PlayerController player = other.GetComponent<PlayerController>();
            if (player != null) {
                if (!playersOnBelt.Contains(player)) {
                    playersOnBelt.Add(player);
                }
                return;
            }
            
            // Проверяем физические объекты
            Rigidbody rb = other.GetComponent<Rigidbody>();
            if (rb != null) {
                if (!objectsOnBelt.Contains(rb)) {
                    objectsOnBelt.Add(rb);
                }
            }
        }
        
        private void OnTriggerExit(Collider other) {
            if (!Object.HasStateAuthority) return;
            
            // Убираем игроков из списка
            PlayerController player = other.GetComponent<PlayerController>();
            if (player != null) {
                playersOnBelt.Remove(player);
                return;
            }
            
            // Убираем физические объекты из списка
            Rigidbody rb = other.GetComponent<Rigidbody>();
            if (rb != null) {
                objectsOnBelt.Remove(rb);
            }
        }
        
        // Публичные методы для управления лентой
        public void SetActive(bool active) {
            if (Object.HasStateAuthority) {
                IsActive = active;
            }
        }
        
        public void SetSpeed(float speed) {
            if (Object.HasStateAuthority) {
                Speed = speed;
            }
        }
        
        public void SetDirection(Vector3 direction) {
            if (Object.HasStateAuthority) {
                beltDirection = direction.normalized;
            }
        }
        
#if UNITY_EDITOR
        private void OnDrawGizmosSelected() {
            // Рисуем направление ленты
            Gizmos.color = Color.green;
            Vector3 worldDirection = transform.TransformDirection(beltDirection);
            Vector3 center = transform.position;
            
            // Рисуем стрелку направления
            Gizmos.DrawRay(center, worldDirection * 2f);
            
            // Рисуем зону обнаружения
            if (beltCollider != null) {
                Gizmos.color = Color.yellow;
                Gizmos.matrix = transform.localToWorldMatrix;
                Vector3 size = beltCollider.size;
                size.y = detectionHeight;
                Gizmos.DrawWireCube(beltCollider.center + Vector3.up * detectionHeight * 0.5f, size);
            }
        }
#endif
    }
}
