using UnityEngine;
using System.Collections.Generic;

public class ConveyorBelt : MonoBehaviour {
    [Header("Conveyor Settings")]
    public Vector3 beltDirection = Vector3.forward;
    public float beltSpeed = 5f;

    private List<Rigidbody> objectsOnBelt = new List<Rigidbody>();

    void Start() {
        // Убеждаемся что коллайдер настроен как триггер
        BoxCollider col = GetComponent<BoxCollider>();
        if (col != null) {
            col.isTrigger = true;
        }
    }

    void FixedUpdate() {
        // Двигаем все объекты на ленте
        Vector3 beltForce = beltDirection.normalized * beltSpeed;

        for (int i = objectsOnBelt.Count - 1; i >= 0; i--) {
            if (objectsOnBelt[i] == null) {
                objectsOnBelt.RemoveAt(i);
                continue;
            }

            Rigidbody rb = objectsOnBelt[i];
            if (!rb.isKinematic) {
                rb.AddForce(beltForce, ForceMode.Acceleration);
            }
        }
    }

    void OnTriggerEnter(Collider other) {
        Rigidbody rb = other.GetComponent<Rigidbody>();
        if (rb != null && !objectsOnBelt.Contains(rb)) {
            objectsOnBelt.Add(rb);
        }
    }

    void OnTriggerExit(Collider other) {
        Rigidbody rb = other.GetComponent<Rigidbody>();
        if (rb != null) {
            objectsOnBelt.Remove(rb);
        }
    }
}
