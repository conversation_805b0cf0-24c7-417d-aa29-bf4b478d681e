# ConveyorBelt - Простая бегущая лента

## Описание
Максимально простой скрипт бегущей ленты, который двигает любые объекты с Rigidbody.

## Настройка

1. Создайте GameObject в сцене (например, Cube)
2. Добавьте компонент `BoxCollider`
3. Добавьте скрипт `ConveyorBelt`
4. Настройте параметры:
   - **Belt Direction** - направление движения (например, Vector3.forward)
   - **Belt Speed** - скорость движения (например, 5)

## Как работает
- Автоматически находит все объекты с Rigidbody, которые касаются ленты
- Применяет к ним силу в заданном направлении
- Работает с любыми предметами, игроками, мячами и т.д.

## Требования
- BoxCollider (автоматически становится триггером)
- Объекты должны иметь Rigidbody для движения
