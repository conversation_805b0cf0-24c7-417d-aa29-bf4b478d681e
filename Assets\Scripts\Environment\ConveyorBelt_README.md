# ConveyorBelt - Бегущая лента

## Описание
Простой скрипт бегущей ленты, который двигает любые объекты, попавшие на неё - как игроков, так и предметы (PickupItem и другие физические объекты).

## Настройка

### 1. Создание объекта ленты
1. Создайте GameObject в сцене
2. Добавьте компонент `BoxCollider` и установите `isTrigger = true`
3. Добавьте компонент `NetworkObject` (для сетевой синхронизации)
4. Добавьте компонент `ConveyorBelt`

### 2. Настройка параметров

#### Conveyor Settings:
- **Belt Direction** - направление движения ленты (локальные координаты)
- **Belt Speed** - скорость движения ленты
- **Is Active** - активна ли лента при старте

#### Detection:
- **Affected Layers** - какие слои объектов будут затронуты лентой
- **Detection Height** - высота зоны обнаружения над лентой

#### Visual:
- **Belt Renderer** - рендерер для анимации текстуры ленты
- **Texture Property Name** - имя свойства текстуры для анимации (обычно "_MainTex")

## Как работает

### Для игроков:
- Использует `PlayerController.RPC_ApplyKnockback()` для применения дополнительной скорости
- Работает только с живыми игроками (`health.IsAlive`)
- Совместимо с системой SimpleKCC

### Для предметов:
- Применяет силу к `Rigidbody` компонентам
- Работает только с не-кинематическими объектами
- Подходит для PickupItem и других физических объектов

### Визуальные эффекты:
- Автоматически анимирует текстуру ленты при движении
- Показывает направление и зону обнаружения в редакторе

## Управление через код

```csharp
ConveyorBelt belt = GetComponent<ConveyorBelt>();

// Включить/выключить ленту
belt.SetActive(true);

// Изменить скорость
belt.SetSpeed(10f);

// Изменить направление
belt.SetDirection(Vector3.right);
```

## Требования
- Unity Fusion (NetworkBehaviour)
- BoxCollider с isTrigger = true
- NetworkObject для сетевой синхронизации

## Совместимость
- Работает с PlayerController и SimpleKCC
- Совместимо с PickupItem и наследниками
- Поддерживает любые объекты с Rigidbody
